export type SwapPaymentType = 'money' | 'one_night_points' | undefined | null

export interface Swap {
	id: number
	created_at: string
	updated_at: string
	request_user_id: number
	user_id: number
	request_user_home: number
	user_home: any
	start_at: string
	end_at: string
	comment: string
	status: string
	payment_status: string
	message_reminder_bool: number
	swap_type: string
	is_waiting_on_payment: number
	is_non_rec_on_site: number
	extra_info: ExtraInfo
	user_from: UserFrom
	user_to: UserTo
	home_from: any
	home_to: HomeTo
}

export interface Booking {
	id: number
	user_id: number
	request_user_home: number
	user_home: null | number
	start_at: string
	end_at: string
	comment: string
	status: string
	code: string
	extra_info: ExtraInfo
	user: UserTo
	fromHome: null | HomeTo
	toHome: HomeTo

	created_at: string
	updated_at: string

	payment_status: string
	message_reminder_bool: number

	swap_type: string
	booking_type: string
	guest_cleaning_fee_enabled: boolean
	host_cleaning_fee_enabled: boolean
	is_waiting_on_payment: number
	is_non_rec_on_site: number

	from_sharable_link: string | null
}

export interface ExtraInfo {
	total_money: number
	paid_loyalty_points: number
	paid_money: number
	total_loyalty_points: number
	pointsToReward: number
	rewardedPoints: number
	transferredPoints: number

	cleaningFee: number
	tax: number
	serviceFee: number
	stripePaymentDescription: string
	paymentType: SwapPaymentType
	hostWillGet: number

	guests: number
	pets: Pet
	priceInfo: {
		nightly_rate: number
		getDiffsInDays: number
		getRawTotalMoney: number
		getCleaningFee: number
		getTaxRate: number
		getTax: number
		getServiceFee: number
		getTotalMoney: number
		hostPaymentAmount: number
		averageNightlyRateOnTotal: number
		paymentIntent: string
		hostPaymentIntent: string
	}
}

export interface UserFrom {
	id: number
	first_name: string
	last_name: string
	about: any
	email: string
	email_verified_at: any
	role_id: number
	group_id: number
	avatar: any
	welcome_send: boolean
	created_at: string
	updated_at: string
	interview_passed: number
	finish_profile_email: number
	shared_url: string
	canonical_url: string
	host_logo: any
	host_landing_page: any
	socials_links: any[]
	profile_completion: number
	home_completion: number
	role: Role
	group: Group
	media: any[]
	socials: any[]
}

export interface Role {
	id: number
	name: string
}

export interface Group {
	id: number
	name: string
	description: string
	created_at: string
	updated_at: string
	is_default: number
}

export interface UserTo {
	id: number
	first_name: string
	last_name: string
	about: string
	email: string
	email_verified_at: any
	role_id: number
	group_id: number
	avatar: string
	welcome_send: boolean
	created_at: string
	updated_at: string
	interview_passed: number
	finish_profile_email: number
	shared_url: string
	canonical_url: string
	host_logo: string
	host_landing_page: string
	socials_links: SocialsLinks
	profile_completion: number
	home_completion: number
	role: Role2
	group: Group2
	media: Medum[]
	socials: Social[]

	venmo_username: string | null

	desired_destinations: number

	extra_info: {
		registration_progress: number
		what_brings_you_to: string[]
	}
}

export interface SocialsLinks {
	linkedin: string
	airbnb: string
	vrbo: string
	other: string
	video_ask_submitted: string
}

export interface Role2 {
	id: number
	name: string
}

export interface Group2 {
	id: number
	name: string
	description: string
	created_at: string
	updated_at: string
	is_default: number
}

export interface Medum {
	id: number
	model_type: string
	model_id: number
	uuid: string
	collection_name: string
	name: string
	file_name: string
	mime_type: string
	disk: string
	conversions_disk: string
	size: number
	manipulations: any[]
	custom_properties: any[]
	generated_conversions: GeneratedConversions
	responsive_images: any[]
	order_column: number
	created_at: string
	updated_at: string
	original_url: string
	preview_url: string
}

export interface GeneratedConversions {
	thumb: boolean
}

export interface Social {
	id: number
	created_at: string
	updated_at: string
	name: string
	value: string
	user_id: number
}

export interface HomeTo {
	id: number
	slug: string
	created_at: string
	updated_at: string
	title: string
	address: string
	country_long: string
	state_long: string
	city_long: string
	street: string
	beds: number
	baths: number
	guests: number
	number_of_beds: number
	location: string
	description: string
	user_id: number
	invitation_id: any
	status: string
	is_filled: number
	ical_url: string | null
	featured: number
	finish_listing_email: number
	address_set: number
	nightly_rate: string | number
	allow_booking: number
	allow_swaps: number
	on_site_booking: number
	booking_from_on_site: boolean
	booking_from_off_site: boolean
	booking_from_white_labeled: boolean
	allow_points: number
	points_price: string
	is_parterize: number
	cleaning_fee: number
	pet_fee: number
	tax_rate: number
	amenities: string[]
	photos: Photo[]
	owner: Owner
	media: Medum3[]
	available_dates: any[]
	user: User
	offer_as_seasonal_lease: boolean
	seasonal_lease_description: string,
	minimum_stay_rentals: number
	minimum_stay_swaps: number
	sharable_password: string
	private_fee: number
	private_description: string
	links: [
		{
			id: number
			home_id: number
			url: string
			rating: number
			type: 'airbnb' | 'vrbo' | 'other' | 'bookingLink'
		},
	]
	extra_info: {
		createProgressStep: string | null | undefined
		pets: Pet
		homeType: string | null | undefined
		typeOfSpaces: SpaceType[]
		additionalInfos: AdditionalInfos
		cancellationPolicies: CancellationPolicies
		checkInOut: CheckInOut
		houseRules: HouseRules
		additionalNotes: string | null | undefined
	} | null
}

interface CancellationPolicies {
	friendsAndFamily: boolean
	publicRental: boolean
	flexiblePublicRental: boolean
	longTermStay: boolean
	nonRefundable: boolean
}

interface CheckInOut {
	checkinTime: string
	checkoutTime: string
}

interface HouseRules {
	noEvents: boolean
	noSmoking: boolean
	quietHours: boolean
	quietHoursTime?: {
		startTime: string
		endTime: string
	}
	additionalRules?: string
}

interface SpaceType {
	title: string
	description: string
}

export interface RentalPlan {
	title: string
	description: string
	note?: string
}

export interface Pet {
	enabled: 'yes' | 'no' | 'service animal only'
	type?: 'dog' | 'cat' | 'goldfish' | undefined | null
	number?: number | undefined | null
}

export interface UploadedFile {
	media_id: number | string
	filename: string
	url: string
}

export interface AdditionalInfos {
	items: string[]
	other: null | string
}

export interface CreateHomeData {
	title: null | string
	typeOfSpace: SpaceType[]
	rentalPlan: RentalPlan[]
	isBooking: boolean
	isSwap: boolean
	isPrivate: boolean
	address: null | string
	bedrooms: number
	beds: number
	bathrooms: number
	guests: number
	pets: Pet
	amenities: string[]
	photos: UploadedFile[]
	description: null | string
	nightlyRate: null | number
	cleaningFee: null | number
	taxRate: null | number
	minimumStay: null | number
	petFee: null | number
	offerAsSeasonalLease: boolean
	offerAsSkiLease: boolean
	additionalInfos: AdditionalInfos
	agreement: boolean
}

export interface EditHomeData {
	id?: number | null
	title: null | string
	status: null | string
	slug: null | string
	sharablePassword: null | string
	typeOfSpace: SpaceType[]
	isBooking: boolean
	isSwap: boolean
	isPrivate: boolean
	isRedirect: boolean
	redirectUrl: null | string
	address: null | string
	bedrooms: number
	beds: number
	bathrooms: number
	guests: number
	pets: Pet
	amenities: string[]
	photos: UploadedFile[]
	description: null | string
	nightlyRate: null | number
	cleaningFee: null | number
	taxRate: null | number
	minimumStay: null | number
	petFee: null | number
	offerAsSeasonalLease: boolean
	offerAsSkiLease: boolean
	additionalInfos: AdditionalInfos
	adjustedPrice: {
		nightlyRate: number | null
		cleaningFee: number | null
		taxRate: number | null
		petFee: number | null
		minimumStay: number | null
	}
	cancellationPolicies: CancellationPolicies
	checkInOut: CheckInOut
	houseRules: HouseRules
}

export interface Photo {
	name: string
	src: string
	thumb: string

	media_id: number | string
	filename: string
	url: string
}

export interface Owner {
	id: number
	first_name: string
	last_name: string
	about: string
	email: string
	email_verified_at: any
	role_id: number
	group_id: number
	avatar: string
	welcome_send: boolean
	created_at: string
	updated_at: string
	interview_passed: number
	finish_profile_email: number
	shared_url: string
	canonical_url: string
	host_logo: string
	host_landing_page: string
	socials_links: SocialsLinks2
	profile_completion: number
	home_completion: number
	role: Role3
	group: Group3
	media: Medum2[]
	socials: Social2[]
}

export interface SocialsLinks2 {
	linkedin: string
	airbnb: string
	vrbo: string
	other: string
	video_ask_submitted: string
}

export interface Role3 {
	id: number
	name: string
}

export interface Group3 {
	id: number
	name: string
	description: string
	created_at: string
	updated_at: string
	is_default: number
}

export interface Medum2 {
	id: number
	model_type: string
	model_id: number
	uuid: string
	collection_name: string
	name: string
	file_name: string
	mime_type: string
	disk: string
	conversions_disk: string
	size: number
	manipulations: any[]
	custom_properties: any[]
	generated_conversions: GeneratedConversions2
	responsive_images: any[]
	order_column: number
	created_at: string
	updated_at: string
	original_url: string
	preview_url: string
}

export interface GeneratedConversions2 {
	thumb: boolean
}

export interface Social2 {
	id: number
	created_at: string
	updated_at: string
	name: string
	value: string
	user_id: number
}

export interface Medum3 {
	id: number
	model_type: string
	model_id: number
	uuid: string
	collection_name: string
	name: string
	file_name: string
	mime_type: string
	disk: string
	conversions_disk: string
	size: number
	manipulations: any[]
	custom_properties: any[]
	generated_conversions: GeneratedConversions3
	responsive_images: any[]
	order_column: number
	created_at: string
	updated_at: string
	original_url: string
	preview_url: string
}

export interface GeneratedConversions3 {
	thumb: boolean
}

export interface User {
	id: number
	first_name: string
	last_name: string
	about: string
	email: string
	email_verified_at: any
	role_id: number
	group_id: number
	avatar: string
	welcome_send: boolean
	created_at: string
	updated_at: string
	interview_passed: number
	finish_profile_email: number
	shared_url: string
	canonical_url: string
	host_logo: string
	host_landing_page: string
	socials_links: SocialsLinks3
	profile_completion: number
	home_completion: number
	role: Role4
	group: Group4
	media: Medum4[]
	socials: Social3[]
	points_balance: number
}

export interface SocialsLinks3 {
	linkedin: string
	airbnb: string
	vrbo: string
	other: string
	video_ask_submitted: string
}

export interface Role4 {
	id: number
	name: string
}

export interface Group4 {
	id: number
	name: string
	description: string
	created_at: string
	updated_at: string
	is_default: number
}

export interface Medum4 {
	id: number
	model_type: string
	model_id: number
	uuid: string
	collection_name: string
	name: string
	file_name: string
	mime_type: string
	disk: string
	conversions_disk: string
	size: number
	manipulations: any[]
	custom_properties: any[]
	generated_conversions: GeneratedConversions4
	responsive_images: any[]
	order_column: number
	created_at: string
	updated_at: string
	original_url: string
	preview_url: string
}

export interface GeneratedConversions4 {
	thumb: boolean
}

export interface Social3 {
	id: number
	created_at: string
	updated_at: string
	name: string
	value: string
	user_id: number
}

type HomeCustomNightlyRateCondition = {
	from_date: Date | null
	to_date: Date | null
	days_of_week: number[] | null
}

type HomeCustomNightlyRate = {
	id: number
	home_id: number
	home?: {
		id: number
		title: string
		nightly_rate: number
	}
	nightly_rate: number
	is_active: boolean
	conditions: HomeCustomNightlyRateCondition
}

export interface Chat {
	avatar: string
	myMessage: boolean
	created_at: string
	body: string
}

export interface MyCrew {
	id: number
	name: string
	email: string
	type: string | null
	mobile_number: string | null
	extra_info: Array<any> | null
	slug: string
	created_at?: Date
	updated_at?: Date
}

export interface MyJob {
	id?: number
	title: string
	description: string
	slug: string
	home_id: number
	created_by_id: number
	home: HomeTo
	service_provider_id?: number
	status: string
	start_time: string
	end_time: string
	price: number
	extra_info: Array<any> | null
	created_at?: Date
	updated_at?: Date
	service_provider?: MyCrew
}

export interface JobInvitation {
	id?: number
	service_job_id: number
	service_provider_id: number
	service_provider_ids: number[]
	service_job: MyJob
	service_provider: MyCrew
	is_accepted: boolean
	decline_reason: string
	alternate_start_time: string
	alternate_price: number
	extra_info: {
		host_done_at: string
		crew_viewed_at: string
		crew_accepted_at: string
		crew_declined_at: string
		crew_decline_reason: string
		crew_alternate_start_time: string
		crew_alternate_price: number
		job_snapshot: MyJob
		service_provider_snapshot: MyCrew
		crew_ready_at: string
		ready_notes: string
		host_cancel_at: string
		crew_cancel_at: string
		cancel_reason: string
		payment_claimed_at: string
	}
	slug: string
	attachments: Array<{
		name: string
		url: string
		mime_type: string
	}>
	created_at?: Date
	updated_at?: Date
}

export interface JobInvitationMessage {
	id?: number
	job_invitation_id: number
	sender_id?: number
	sender?: UserTo
	content: string
	created_at: string
	updated_at?: string
}

export interface JobInvitationPropose {
	id?: number
	job_invitation_id: number
	sender_id?: number
	sender?: UserTo
	alternate_start_time: string
	alternate_price: number
	extra_info: {
		accepted_at: string
		declined_at: string
	}
	created_at: string
	updated_at?: string
}

export interface StripeStatus {
	is_connected: boolean
	charges_enabled: boolean
	payouts_enabled: boolean
}

export interface PaymentInfo {
	totalAmount: number
	applicationFeeAmount: number
	applicationFeePercent: number
	stripeConnectId: string
}

export type ProfileAttribute =
	| 'first_name'
	| 'last_name'
	| 'email'
	| 'avatar'
	| 'about'
	| 'interview_passed'

export interface HostDashboardData {
	userFirstName: string
	totalListings: number
	totalBookings: number
	totalUnreadMessages: number
	totalCrewAlerts: number
	totalActiveBookings: number
	totalUpcomingCheckIns: number
	totalUpcomingCheckOuts: number
	totalPendingReviews: number
	profileCompletion: number
	profileCompletedAttrs: ProfileAttribute[]
	profileNotCompletedAttrs: ProfileAttribute[]

	extraInfo: {
		registration_progress: number
	}
}

export type ViewMode = 'host' | 'traveler'

export interface SharableLink {
	id: number
	home_id: number
	link: string
	price_info: {
		nightly_rate: number
		cleaning_fee: number
		tax_rate: number
		minimum_stay: number
		pet_fee: number
	}
	created_at: string
	updated_at: string
}

export type SwapOption = {
	label: string
	value: string
}

export interface GuestItem {
	id: number
	guest: UserTo
	accessible_sharable_links: AccessibleSharableLink[]
	deleted_at: string | null
}

export interface AccessibleSharableLink {
	id: number
	link: string
	is_allowed: boolean
	created_at: string
	home: {
		id: number
		title: string
		slug: string
		photos: Photo[]
	}
	price_info: {
		nightly_rate: number
		cleaning_fee: number
		pet_fee: number
		tax_rate: number
		minimum_stay: number
	}
	guestBookings?: Booking[]
}

export interface MessageV2 {
	id: number
	sender: UserTo
	receiver: UserTo
	receiver_id: number
	sender_id: number
	messageable: Booking // The type of messageable can be more specific if known
	messageable_type: string
	messageable_id: number
	subject: string
	body: string
	viewed: boolean
	created_at: string
	updated_at: string

	archived_by_sender: boolean
	archived_by_receiver: boolean
}

// types/subscription.ts
export interface SubscriptionPlan {
	id: string
	price: number
	interval: 'month' | 'year'
	description: string
}

export interface Subscription {
	id: number
	user_id: number
	type: string
	stripe_id: string
	stripe_status: 'active' | 'canceled' | 'incomplete' | 'past_due'
	stripe_price: string
	quantity: number
	trial_ends_at: string | null
	ends_at: string | null
	created_at: string
	updated_at: string
	items: SubscriptionItem[]
}

export interface SubscriptionItem {
	id: number
	subscription_id: number
	stripe_id: string
	stripe_product: string
	stripe_price: string
	quantity: number
	created_at: string
	updated_at: string
}

export interface PromoCodeValidation {
	valid: boolean
	message: string
	coupon_id?: string
	discount_type?: 'percent' | 'amount'
	discount_value?: number
	original_price?: number
	discounted_price?: number
}

export interface SubscriptionState {
	subscription: Subscription | null
	hasSubscription: boolean
	loading: boolean
	showPaywallModal: boolean
	promoCode: string
	promoCodeValidation: PromoCodeValidation | null
}
